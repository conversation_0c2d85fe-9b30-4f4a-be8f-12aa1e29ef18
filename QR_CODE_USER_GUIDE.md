# دليل المستخدم - وظيفة QR Code في تصفية برو

## 📋 نظرة عامة

تم إضافة وظيفة رموز QR Code إلى نظام تصفية برو لتمكين المستخدمين من عرض التصفيات والتقارير على الهاتف المحمول بسهولة. عند طباعة أي تصفية أو تقرير، سيتم إضافة رمز QR Code يحتوي على جميع البيانات المطلوبة.

## 🔲 ما هو QR Code؟

رمز QR Code هو رمز مربع يمكن مسحه بكاميرا الهاتف المحمول لعرض المعلومات المخزنة فيه. في تصفية برو، يحتوي QR Code على:

- **للتصفيات**: معلومات الكاشير، التاريخ، الملخص المالي، وعدد العمليات
- **للتقارير**: عنوان التقرير، نطاق التاريخ، عدد التصفيات، والمجاميع

## ⚙️ إعداد QR Code

### الوصول للإعدادات
1. انتقل إلى قسم **"الإعدادات"** من القائمة الجانبية
2. اختر تبويب **"إعدادات QR Code"** 🔲

### الإعدادات المتاحة

#### **الإعدادات العامة**
- **تفعيل رموز QR Code**: تشغيل/إيقاف إضافة QR Code للطباعة
- **موضع رمز QR Code**: اختيار مكان ظهور الرمز في الصفحة المطبوعة
  - أسفل اليسار (افتراضي)
  - أسفل اليمين
  - أعلى اليسار
  - أعلى اليمين
- **حجم رمز QR Code**: تحديد حجم الرمز (80-200 بكسل)
- **المسافة من الحافة**: تحديد المسافة بين الرمز وحافة الصفحة

#### **إعدادات الخادم**
- **رابط الخادم الأساسي**: الرابط الذي سيتم توجيه المستخدمين إليه
- **مستوى تصحيح الأخطاء**: جودة الرمز ومقاومته للتلف

### حفظ الإعدادات
اضغط **"حفظ إعدادات QR Code"** لتطبيق التغييرات.

## 📱 استخدام QR Code

### طباعة التصفيات والتقارير
1. عند طباعة أي تصفية أو تقرير، سيظهر QR Code تلقائياً في الموضع المحدد
2. تأكد من أن الطباعة واضحة وأن QR Code مقروء

### مسح QR Code بالهاتف
1. افتح كاميرا الهاتف المحمول
2. وجه الكاميرا نحو QR Code
3. اضغط على الرابط الذي يظهر على الشاشة
4. ستفتح صفحة ويب تعرض نفس البيانات المطبوعة

### عرض البيانات على الهاتف
بعد مسح QR Code، ستظهر صفحة متجاوبة تحتوي على:

#### **للتصفيات**:
- معلومات التصفية (رقم التصفية، الكاشير، المحاسب، التاريخ)
- الملخص المالي (إجمالي المقبوضات، مبيعات النظام، الفائض/العجز)
- تفاصيل إضافية (عدد العمليات في كل قسم)

#### **للتقارير**:
- معلومات التقرير (العنوان، نطاق التاريخ، عدد التصفيات)
- ملخص التقرير (إجمالي المبلغ، عدد التصفيات)
- فلاتر التقرير المستخدمة

## 🧪 اختبار النظام

### اختبار إنشاء QR Code
1. في إعدادات QR Code، اضغط **"إنشاء رمز تجريبي"**
2. سيظهر QR Code تجريبي في منطقة المعاينة
3. يمكنك مسح هذا الرمز لاختبار النظام

### اختبارات النظام
- **اختبار الإنشاء**: يختبر قدرة النظام على إنشاء QR Code
- **اختبار فك التشفير**: يختبر قدرة النظام على فك تشفير البيانات

## 🔧 استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها

#### **QR Code لا يظهر في الطباعة**
- تأكد من تفعيل QR Code في الإعدادات
- تحقق من أن حجم QR Code مناسب (120 بكسل موصى به)
- تأكد من أن الطابعة تدعم الطباعة الملونة أو الرمادية

#### **لا يمكن مسح QR Code**
- تأكد من وضوح الطباعة
- تحقق من الإضاءة عند المسح
- جرب زيادة حجم QR Code في الإعدادات
- تأكد من أن مستوى تصحيح الأخطاء مناسب (M أو H)

#### **صفحة الهاتف لا تفتح**
- تحقق من اتصال الهاتف بالإنترنت
- تأكد من أن رابط الخادم صحيح في الإعدادات
- جرب مسح QR Code مرة أخرى

#### **البيانات لا تظهر بشكل صحيح**
- تحقق من أن التصفية أو التقرير محفوظ بشكل صحيح
- تأكد من أن جميع البيانات المطلوبة موجودة
- جرب إعادة إنشاء QR Code

### إعادة تعيين الإعدادات
إذا واجهت مشاكل، يمكنك:
1. الذهاب إلى إعدادات QR Code
2. الضغط على **"إعادة تعيين"**
3. حفظ الإعدادات الافتراضية

## 📊 نصائح للاستخدام الأمثل

### للحصول على أفضل النتائج:
1. **استخدم حجم QR Code 120-150 بكسل** للطباعة العادية
2. **اختر موضع "أسفل اليسار"** لسهولة الوصول
3. **تأكد من جودة الطباعة** لضمان قراءة الرمز
4. **اختبر QR Code** قبل طباعة كميات كبيرة
5. **احتفظ برابط الخادم محدث** في الإعدادات

### للشركات الكبيرة:
- يمكن تخصيص رابط الخادم ليشير إلى خادم الشركة
- يمكن تعديل حجم وموضع QR Code حسب تصميم الشركة
- يُنصح بإجراء اختبارات دورية للتأكد من عمل النظام

## 🔒 الأمان والخصوصية

### حماية البيانات:
- جميع البيانات في QR Code مشفرة
- لا يتم تخزين البيانات الحساسة في QR Code
- يتم عرض ملخص البيانات فقط، وليس التفاصيل الكاملة

### التحكم في الوصول:
- QR Code يعرض معلومات عامة فقط
- لا يمكن الوصول لبيانات حساسة من خلال QR Code
- يمكن إيقاف QR Code تماماً إذا لم تكن هناك حاجة إليه

## 📞 الدعم الفني

إذا واجهت أي مشاكل أو كان لديك أسئلة:

1. **راجع هذا الدليل** للحلول الشائعة
2. **استخدم اختبارات النظام** في إعدادات QR Code
3. **تحقق من سجلات النظام** في وحدة تحكم المطور (F12)
4. **تواصل مع الدعم الفني** مع تفاصيل المشكلة

---

## 📝 ملاحظات إضافية

- هذه الوظيفة متاحة في الإصدار 2.0.0 وما بعده من تصفية برو
- يتطلب اتصال بالإنترنت لعرض البيانات على الهاتف المحمول
- يدعم جميع الهواتف الذكية الحديثة التي تحتوي على كاميرا

---

**جميع الحقوق محفوظة © 2025 - تطوير محمد أمين الكامل - نظام تصفية برو**

---

# التوثيق التقني - QR Code System

## 🏗️ هيكل النظام

### الملفات الرئيسية
```
src/
├── qr-data-manager.js      # إدارة تشفير وضغط البيانات
├── qr-generator.js         # إنشاء رموز QR Code
├── qr-data-manager-client.js # فك التشفير في المتصفح
├── mobile-viewer.html      # واجهة الهاتف المحمول
├── mobile-viewer.js        # منطق العارض المتجاوب
├── mobile-server.js        # خادم الواجهة المتجاوبة
└── qr-code-test.js        # مجموعة الاختبارات
```

## 🔧 المكونات التقنية

### 1. QRDataManager
**الغرض**: تشفير وضغط بيانات التصفيات والتقارير

**الوظائف الرئيسية**:
- `encodeReconciliationData()`: تشفير بيانات التصفية
- `encodeReportData()`: تشفير بيانات التقرير
- `decodeData()`: فك تشفير البيانات
- `sanitizeData()`: تنظيف البيانات قبل التشفير

**التقنيات المستخدمة**:
- AES-256-CBC للتشفير
- gzip للضغط
- MD5 للـ checksum

### 2. QRGenerator
**الغرض**: إنشاء رموز QR Code مع خيارات التخصيص

**الوظائف الرئيسية**:
- `generateReconciliationQR()`: إنشاء QR للتصفية
- `generateReportQR()`: إنشاء QR للتقرير
- `generateCustomQR()`: إنشاء QR مخصص

**المكتبات المستخدمة**:
- `qrcode`: إنشاء QR Code
- `canvas`: معالجة الصور

### 3. PrintManager Integration
**التحديثات**:
- إضافة `qrSettings` للإعدادات
- دمج QR Code في HTML الطباعة
- دعم مواضع مختلفة للـ QR Code

### 4. Mobile Server
**الغرض**: خادم ويب لعرض البيانات على الهاتف

**المميزات**:
- خادم HTTP بسيط
- دعم CORS
- معالجة أخطاء شاملة
- API لفك التشفير

## 📡 API Reference

### IPC Handlers (main.js)

#### `save-qr-settings`
```javascript
// حفظ إعدادات QR Code
ipcRenderer.invoke('save-qr-settings', {
  enabled: boolean,
  position: string,
  size: number,
  margin: number,
  baseUrl: string,
  errorLevel: string
})
```

#### `get-qr-settings`
```javascript
// تحميل إعدادات QR Code
const result = await ipcRenderer.invoke('get-qr-settings');
// Returns: { success: boolean, settings: object }
```

#### `generate-test-qr`
```javascript
// إنشاء QR Code تجريبي
const result = await ipcRenderer.invoke('generate-test-qr', testData);
// Returns: { success: boolean, qrCode: string }
```

#### `test-qr-generation`
```javascript
// اختبار إنشاء QR Code
const result = await ipcRenderer.invoke('test-qr-generation');
// Returns: { success: boolean, time: number }
```

#### `test-qr-decoding`
```javascript
// اختبار فك تشفير QR Code
const result = await ipcRenderer.invoke('test-qr-decoding');
// Returns: { success: boolean, time: number, dataType: string }
```

## 🗄️ قاعدة البيانات

### جدول الإعدادات
```sql
-- إعدادات QR Code في جدول settings
INSERT INTO settings (key, value) VALUES
('qr.enabled', '1'),
('qr.position', 'bottom-right'),
('qr.size', '120'),
('qr.margin', '10'),
('qr.baseUrl', 'http://localhost:3000'),
('qr.errorLevel', 'M');
```

## 🔐 الأمان

### التشفير
- **الخوارزمية**: AES-256-CBC
- **المفتاح**: مفتاح ثابت (يُنصح بتغييره في الإنتاج)
- **IV**: عشوائي لكل عملية تشفير

### البيانات المحفوظة
- **التصفيات**: معلومات أساسية + ملخص مالي + عدد العمليات
- **التقارير**: عنوان + نطاق تاريخ + إحصائيات عامة
- **لا يتم حفظ**: بيانات حساسة أو تفاصيل كاملة

## 🧪 الاختبارات

### تشغيل الاختبارات
```javascript
// في وحدة تحكم المتصفح
runQRCodeComprehensiveTest()
```

### أنواع الاختبارات
1. **اختبارات أساسية**: تشفير، فك تشفير، إنشاء QR
2. **اختبارات التكامل**: الطباعة، العارض المتجاوب
3. **اختبارات الأداء**: سرعة الإنشاء، حجم البيانات
4. **اختبارات التوافق**: المتصفحات، الأجهزة

## 🚀 التطوير والتحسين

### إضافة ميزات جديدة
1. **تحديث QRDataManager** لدعم أنواع بيانات جديدة
2. **تحديث QRGenerator** لخيارات تخصيص إضافية
3. **تحديث Mobile Viewer** لعرض البيانات الجديدة
4. **إضافة اختبارات** للميزات الجديدة

### تحسين الأداء
- **ضغط البيانات**: استخدام خوارزميات ضغط أفضل
- **تحسين QR Code**: تقليل حجم البيانات
- **Cache**: حفظ QR Codes المُنشأة مؤقتاً

### التخصيص للشركات
- **مفاتيح تشفير مخصصة**: لكل شركة
- **تصميم QR Code**: إضافة شعار الشركة
- **خادم مخصص**: استضافة على خادم الشركة

## 🔧 استكشاف الأخطاء

### سجلات النظام
```javascript
// تفعيل السجلات المفصلة
console.log('[QR-DEBUG] تفعيل وضع التطوير');
```

### أخطاء شائعة
1. **فشل التشفير**: تحقق من المفتاح والبيانات
2. **QR Code غير مقروء**: زيادة مستوى تصحيح الأخطاء
3. **خادم غير متاح**: تحقق من المنفذ والإعدادات

## 📦 التوزيع

### متطلبات الإنتاج
- Node.js 16+
- مكتبات: qrcode, canvas, crypto
- منفذ مفتوح للخادم المتجاوب

### إعداد الخادم
```javascript
// تخصيص إعدادات الخادم
const server = new MobileServer(3000);
server.start();
```

---

**للمطورين**: هذا التوثيق يغطي الجوانب التقنية الأساسية. للتفاصيل الإضافية، راجع التعليقات في الكود المصدري.
