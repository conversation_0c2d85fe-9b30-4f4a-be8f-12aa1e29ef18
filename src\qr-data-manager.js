// ===================================================
// 🧾 تطبيق: تصفية برو
// 🛠️ المطور: محمد أمين الكامل
// 🗓️ سنة: 2025
// 📌 جميع الحقوق محفوظة
// يمنع الاستخدام أو التعديل دون إذن كتابي
// ===================================================

/**
 * QR Data Manager - إدارة تشفير وضغط بيانات QR Code
 * يتعامل مع تشفير وضغط بيانات التصفيات والتقارير لتضمينها في QR Code
 */

const crypto = require('crypto');
const zlib = require('zlib');

class QRDataManager {
    constructor() {
        // مفتاح التشفير الافتراضي (يجب تغييره في الإنتاج)
        this.encryptionKey = 'TasfiyaPro2025QRCodeSecretKey123456';
        this.algorithm = 'aes-256-cbc';
        this.maxDataSize = 2000; // الحد الأقصى لحجم البيانات في QR Code

        console.log('🔐 [QR-DATA] تم تهيئة مدير بيانات QR Code');
    }

    /**
     * تشفير وضغط بيانات التصفية
     * @param {Object} reconciliationData - بيانات التصفية
     * @param {Object} options - خيارات إضافية
     * @returns {string} البيانات المشفرة والمضغوطة
     */
    encodeReconciliationData(reconciliationData, options = {}) {
        try {
            console.log('🔐 [QR-DATA] بدء تشفير بيانات التصفية...');

            // تحضير البيانات للتشفير
            const dataToEncode = {
                type: 'reconciliation',
                version: '1.0',
                timestamp: new Date().toISOString(),
                data: this.sanitizeReconciliationData(reconciliationData),
                options: options
            };

            // تحويل إلى JSON
            const jsonData = JSON.stringify(dataToEncode);
            console.log(`📊 [QR-DATA] حجم البيانات الأصلية: ${jsonData.length} بايت`);

            // ضغط البيانات
            const compressedData = zlib.gzipSync(jsonData);
            console.log(`🗜️ [QR-DATA] حجم البيانات المضغوطة: ${compressedData.length} بايت`);

            // تشفير البيانات
            let encryptedData;
            try {
                encryptedData = this.encrypt(compressedData);
                console.log(`🔒 [QR-DATA] حجم البيانات المشفرة: ${encryptedData.length} حرف`);
            } catch (encryptError) {
                console.warn('⚠️ [QR-DATA] فشل التشفير المتقدم، استخدام Base64:', encryptError.message);
                // استخدام Base64 كبديل
                encryptedData = Buffer.from(compressedData).toString('base64');
                console.log(`🔒 [QR-DATA] حجم البيانات Base64: ${encryptedData.length} حرف`);
            }

            // التحقق من حجم البيانات
            if (encryptedData.length > this.maxDataSize) {
                console.warn(`⚠️ [QR-DATA] حجم البيانات كبير: ${encryptedData.length} > ${this.maxDataSize}`);
                // محاولة ضغط إضافي
                return this.compressForQR(dataToEncode);
            }

            return encryptedData;

        } catch (error) {
            console.error('❌ [QR-DATA] خطأ في تشفير البيانات:', error);
            throw new Error('فشل في تشفير بيانات التصفية');
        }
    }

    /**
     * تشفير وضغط بيانات التقرير
     * @param {Object} reportData - بيانات التقرير
     * @param {Object} filters - فلاتر التقرير
     * @returns {string} البيانات المشفرة والمضغوطة
     */
    encodeReportData(reportData, filters = {}) {
        try {
            console.log('🔐 [QR-DATA] بدء تشفير بيانات التقرير...');

            const dataToEncode = {
                type: 'report',
                version: '1.0',
                timestamp: new Date().toISOString(),
                filters: filters,
                data: this.sanitizeReportData(reportData)
            };

            const jsonData = JSON.stringify(dataToEncode);
            console.log(`📊 [QR-DATA] حجم بيانات التقرير: ${jsonData.length} بايت`);

            const compressedData = zlib.gzipSync(jsonData);

            let encryptedData;
            try {
                encryptedData = this.encrypt(compressedData);
            } catch (encryptError) {
                console.warn('⚠️ [QR-DATA] فشل التشفير المتقدم للتقرير، استخدام Base64:', encryptError.message);
                encryptedData = Buffer.from(compressedData).toString('base64');
            }

            if (encryptedData.length > this.maxDataSize) {
                return this.compressForQR(dataToEncode);
            }

            return encryptedData;

        } catch (error) {
            console.error('❌ [QR-DATA] خطأ في تشفير بيانات التقرير:', error);
            throw new Error('فشل في تشفير بيانات التقرير');
        }
    }

    /**
     * فك تشفير وإلغاء ضغط البيانات
     * @param {string} encryptedData - البيانات المشفرة
     * @returns {Object} البيانات الأصلية
     */
    decodeData(encryptedData) {
        try {
            console.log('🔓 [QR-DATA] بدء فك تشفير البيانات...');

            // فك التشفير
            const compressedData = this.decrypt(encryptedData);

            // إلغاء الضغط
            const jsonData = zlib.gunzipSync(compressedData).toString();

            // تحويل من JSON
            const decodedData = JSON.parse(jsonData);

            console.log(`✅ [QR-DATA] تم فك التشفير بنجاح - النوع: ${decodedData.type}`);
            return decodedData;

        } catch (error) {
            console.error('❌ [QR-DATA] خطأ في فك التشفير:', error);
            throw new Error('فشل في فك تشفير البيانات');
        }
    }

    /**
     * تشفير البيانات
     * @param {Buffer} data - البيانات للتشفير
     * @returns {string} البيانات المشفرة
     */
    encrypt(data) {
        try {
            const iv = crypto.randomBytes(16);
            const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
            const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);

            let encrypted = cipher.update(data);
            encrypted = Buffer.concat([encrypted, cipher.final()]);

            return iv.toString('hex') + ':' + encrypted.toString('hex');
        } catch (error) {
            console.error('❌ [QR-DATA] خطأ في التشفير:', error);
            // Fallback: استخدام Base64 بسيط
            return Buffer.from(data).toString('base64');
        }
    }

    /**
     * فك تشفير البيانات
     * @param {string} encryptedData - البيانات المشفرة
     * @returns {Buffer} البيانات المفكوكة
     */
    decrypt(encryptedData) {
        try {
            // التحقق من وجود IV
            if (!encryptedData.includes(':')) {
                // Fallback: فك Base64 بسيط
                return Buffer.from(encryptedData, 'base64');
            }

            const parts = encryptedData.split(':');
            const iv = Buffer.from(parts[0], 'hex');
            const encrypted = Buffer.from(parts[1], 'hex');

            const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
            const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);

            let decrypted = decipher.update(encrypted);
            decrypted = Buffer.concat([decrypted, decipher.final()]);

            return decrypted;
        } catch (error) {
            console.error('❌ [QR-DATA] خطأ في فك التشفير:', error);
            // Fallback: فك Base64 بسيط
            try {
                return Buffer.from(encryptedData, 'base64');
            } catch (base64Error) {
                throw new Error('فشل في فك تشفير البيانات');
            }
        }
    }

    /**
     * تنظيف بيانات التصفية (إزالة البيانات غير الضرورية)
     * @param {Object} data - بيانات التصفية
     * @returns {Object} البيانات المنظفة
     */
    sanitizeReconciliationData(data) {
        return {
            id: data.id,
            cashier_id: data.cashier_id,
            cashier_name: data.cashier_name,
            accountant_id: data.accountant_id,
            reconciliation_date: data.reconciliation_date,
            branch_id: data.branch_id,
            status: data.status,
            // البيانات المالية الأساسية فقط
            summary: data.summary,
            // عدد العناصر بدلاً من البيانات الكاملة
            counts: {
                bankReceipts: data.bankReceipts?.length || 0,
                cashReceipts: data.cashReceipts?.length || 0,
                postpaidSales: data.postpaidSales?.length || 0,
                customerReceipts: data.customerReceipts?.length || 0,
                returnInvoices: data.returnInvoices?.length || 0,
                suppliers: data.suppliers?.length || 0
            }
        };
    }

    /**
     * تنظيف بيانات التقرير
     * @param {Object} data - بيانات التقرير
     * @returns {Object} البيانات المنظفة
     */
    sanitizeReportData(data) {
        return {
            title: data.title,
            dateRange: data.dateRange,
            summary: data.summary,
            reconciliationsCount: data.reconciliations?.length || 0,
            totalAmount: data.totalAmount,
            generatedAt: new Date().toISOString()
        };
    }

    /**
     * ضغط إضافي للبيانات الكبيرة
     * @param {Object} data - البيانات للضغط
     * @returns {string} البيانات المضغوطة بقوة
     */
    compressForQR(data) {
        // إزالة البيانات غير الأساسية
        const minimalData = {
            type: data.type,
            id: data.data.id,
            key: data.data.cashier_id + '_' + data.data.reconciliation_date,
            checksum: this.generateChecksum(data)
        };

        const jsonData = JSON.stringify(minimalData);
        const compressedData = zlib.deflateSync(jsonData);
        return this.encrypt(compressedData);
    }

    /**
     * إنشاء checksum للبيانات
     * @param {Object} data - البيانات
     * @returns {string} checksum
     */
    generateChecksum(data) {
        const hash = crypto.createHash('md5');
        hash.update(JSON.stringify(data));
        return hash.digest('hex').substring(0, 8);
    }

    /**
     * التحقق من صحة البيانات المفكوكة
     * @param {Object} data - البيانات المفكوكة
     * @returns {boolean} صحة البيانات
     */
    validateDecodedData(data) {
        if (!data || typeof data !== 'object') return false;
        if (!data.type || !data.version || !data.timestamp) return false;
        if (!['reconciliation', 'report'].includes(data.type)) return false;
        return true;
    }

    /**
     * إنشاء URL للعرض على الهاتف المحمول
     * @param {string} encodedData - البيانات المشفرة
     * @param {string} baseUrl - URL الأساسي
     * @returns {string} URL كامل
     */
    generateViewUrl(encodedData, baseUrl = 'http://localhost:3000') {
        return `${baseUrl}/view?data=${encodeURIComponent(encodedData)}`;
    }
}

module.exports = QRDataManager;
