// فحص إعدادات QR Code في قاعدة البيانات
const Database = require('better-sqlite3');
const path = require('path');
const os = require('os');

// مسار قاعدة البيانات
const dbPath = path.join(os.homedir(), 'AppData', 'Roaming', 'casher', 'casher.db');

console.log('🔍 فحص إعدادات QR Code...');
console.log('📁 مسار قاعدة البيانات:', dbPath);

try {
    const db = new Database(dbPath);
    
    // فحص إعدادات QR Code
    const qrSettings = db.prepare('SELECT key, value FROM settings WHERE key LIKE ?').all('qr.%');
    
    console.log('\n📊 إعدادات QR Code الحالية:');
    if (qrSettings.length === 0) {
        console.log('❌ لا توجد إعدادات QR Code في قاعدة البيانات');
        
        // إدراج الإعدادات الافتراضية
        console.log('\n💾 إدراج الإعدادات الافتراضية...');
        const defaultSettings = [
            ['qr.enabled', '1'],
            ['qr.position', 'bottom-right'],
            ['qr.size', '120'],
            ['qr.margin', '10'],
            ['qr.baseUrl', 'http://localhost:3000'],
            ['qr.errorLevel', 'M']
        ];
        
        const insertStmt = db.prepare('INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, datetime("now"))');
        
        for (const [key, value] of defaultSettings) {
            insertStmt.run(key, value);
            console.log(`✅ تم إدراج: ${key} = ${value}`);
        }
        
        console.log('\n✅ تم إدراج جميع الإعدادات الافتراضية');
    } else {
        qrSettings.forEach(setting => {
            console.log(`✅ ${setting.key}: ${setting.value}`);
        });
        
        // التحقق من تفعيل QR Code
        const enabledSetting = qrSettings.find(s => s.key === 'qr.enabled');
        if (enabledSetting) {
            if (enabledSetting.value === '1') {
                console.log('\n🔲 QR Code مُفعل في الإعدادات ✅');
            } else {
                console.log('\n❌ QR Code مُعطل في الإعدادات');
                console.log('💡 لتفعيله، اذهب إلى الإعدادات → إعدادات QR Code');
            }
        } else {
            console.log('\n⚠️ إعداد التفعيل غير موجود، سيتم إضافته...');
            db.prepare('INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, datetime("now"))').run('qr.enabled', '1');
            console.log('✅ تم تفعيل QR Code');
        }
    }
    
    db.close();
    console.log('\n🎯 انتهى الفحص');
    
} catch (error) {
    console.error('❌ خطأ في فحص قاعدة البيانات:', error.message);
}
