// ===================================================
// 🧾 تطبيق: تصفية برو
// 🛠️ المطور: محمد أمين الكامل
// 🗓️ سنة: 2025
// 📌 جميع الحقوق محفوظة
// يمنع الاستخدام أو التعديل دون إذن كتابي
// ===================================================

const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const DatabaseManager = require('./database');
const PDFGenerator = require('./pdf-generator');
const PrintManager = require('./print-manager');
const MobileServer = require('./mobile-server');

// ===================================================================
// DATE AND NUMBER FORMATTING UTILITIES - GREGORIAN CALENDAR ONLY
// ===================================================================

/**
 * Format date using Gregorian calendar only (DD/MM/YYYY format)
 */
function formatDate(dateString) {
    if (!dateString) return 'غير محدد';

    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return 'غير محدد';

        // Format as DD/MM/YYYY using English numbers
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();

        return `${day}/${month}/${year}`;
    } catch (error) {
        console.error('Error formatting date:', error);
        return 'غير محدد';
    }
}

/**
 * Format date and time using Gregorian calendar only
 */
function formatDateTime(dateTimeString) {
    if (!dateTimeString) return 'غير محدد';

    try {
        const date = new Date(dateTimeString);
        if (isNaN(date.getTime())) return 'غير محدد';

        // Format as DD/MM/YYYY HH:MM using English numbers
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        return `${day}/${month}/${year} ${hours}:${minutes}`;
    } catch (error) {
        console.error('Error formatting datetime:', error);
        return 'غير محدد';
    }
}

/**
 * Format numbers using English numerals
 */
function formatNumber(number) {
    if (number === null || number === undefined) return '0';

    try {
        // Use English locale for number formatting
        return new Intl.NumberFormat('en-US').format(number);
    } catch (error) {
        console.error('Error formatting number:', error);
        return String(number);
    }
}

/**
 * Get current date in DD/MM/YYYY format using Gregorian calendar
 */
function getCurrentDate() {
    return formatDate(new Date());
}

/**
 * Get current date and time in DD/MM/YYYY HH:MM format using Gregorian calendar
 */
function getCurrentDateTime() {
    return formatDateTime(new Date());
}

/**
 * Format currency amounts using English numerals
 */
function formatCurrency(amount) {
    if (amount === null || amount === undefined) return '0.00';

    try {
        const numericAmount = parseFloat(amount);
        if (isNaN(numericAmount)) return '0.00';

        // Format with 2 decimal places using English numbers
        return numericAmount.toFixed(2);
    } catch (error) {
        console.error('Error formatting currency:', error);
        return '0.00';
    }
}

/**
 * Format decimal numbers (percentages, averages, etc.) using English numerals
 */
function formatDecimal(value, decimalPlaces = 2) {
    if (value === null || value === undefined) return '0.00';

    try {
        const numericValue = parseFloat(value);
        if (isNaN(numericValue)) return '0.00';

        // Format with specified decimal places using English numbers
        return numericValue.toFixed(decimalPlaces);
    } catch (error) {
        console.error('Error formatting decimal:', error);
        return '0.00';
    }
}

// Set environment variables for proper development/production detection
if (!process.env.NODE_ENV) {
  // Check if running in development mode
  const isDev = process.argv.includes('--dev') || !app.isPackaged;
  process.env.NODE_ENV = isDev ? 'development' : 'production';
}

console.log(`🚀 Application starting in ${process.env.NODE_ENV} mode`);
console.log(`📦 App is packaged: ${app.isPackaged}`);
console.log(`🔧 Command line args: ${process.argv.join(' ')}`);

// Ensure test scripts are not loaded in production
if (process.env.NODE_ENV === 'production') {
  console.log('🔒 Production mode: Test scripts will be disabled for optimal performance');
}

// Keep a global reference of the window object
let mainWindow;
let printPreviewWindow;
let dbManager;
let pdfGenerator;
let printManager;
let mobileServer;

function createWindow() {
  // Create the browser window with Arabic RTL support
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    title: 'تصفية برو',
    show: false
  });

  // Load the index.html of the app
  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Open DevTools in development
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }

  // Emitted when the window is closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Create print preview window
function createPrintPreviewWindow(printData) {
  console.log('🖨️ [PRINT] إنشاء نافذة معاينة الطباعة...');

  // Close existing print preview window if open
  if (printPreviewWindow && !printPreviewWindow.isDestroyed()) {
    console.log('🖨️ [PRINT] إغلاق نافذة المعاينة السابقة...');
    printPreviewWindow.close();
    printPreviewWindow = null;
  }

  // Create new print preview window
  printPreviewWindow = new BrowserWindow({
    width: 900,
    height: 1200,
    minWidth: 800,
    minHeight: 1000,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    title: 'معاينة الطباعة - Print Preview',
    icon: path.join(__dirname, '../assets/icon.png'),
    parent: mainWindow,
    modal: false,
    show: false,
    autoHideMenuBar: true,
    webSecurity: false
  });

  // Generate print HTML content
  const printHtml = generatePrintHtml(printData);

  // Load the print content
  printPreviewWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(printHtml)}`);

  // Show window when ready
  printPreviewWindow.once('ready-to-show', () => {
    console.log('✅ [PRINT] نافذة معاينة الطباعة جاهزة');
    printPreviewWindow.show();
    printPreviewWindow.focus();
  });

  // Handle window closed
  printPreviewWindow.on('closed', () => {
    console.log('🖨️ [PRINT] تم إغلاق نافذة معاينة الطباعة');
    printPreviewWindow = null;
  });

  // Handle any errors
  printPreviewWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('❌ [PRINT] خطأ في تحميل نافذة المعاينة:', errorCode, errorDescription);
  });

  return printPreviewWindow;
}

// Generate print HTML with Arabic RTL support and A4 formatting
function generatePrintHtml(printData) {
  console.log('📄 [PRINT] إنشاء محتوى HTML للطباعة...');

  const { reconciliation, sections, options } = printData;

  // Validate required data
  if (!reconciliation) {
    throw new Error('بيانات التصفية مطلوبة للطباعة');
  }

  const currentDate = getCurrentDate();
  const currentTime = getCurrentDateTime();

  return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تصفية برو - ${reconciliation.cashier_name}</title>
    <style>
        /* Print-optimized CSS with Arabic support */
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Arabic', 'Arial', sans-serif;
            font-size: 10px;
            line-height: 1.3;
            color: #222;
            direction: rtl;
            text-align: right;
            background: white;
            padding: 8mm;
            max-width: 210mm; /* A4 width */
            margin: 0 auto;
            font-weight: 400;
        }

        @page {
            size: A4;
            margin: 8mm;
        }

        @media print {
            body {
                padding: 0;
                margin: 0;
            }

            .no-print {
                display: none !important;
            }

            .page-break {
                page-break-before: always;
            }
        }

        .header {
            text-align: center;
            margin-bottom: 8px;
            border-bottom: 1px solid #2c3e50;
            padding-bottom: 6px;
        }

        .header h1 {
            font-size: 16px;
            font-weight: 800;
            color: #1a252f;
            margin-bottom: 4px;
            text-shadow: 0.5px 0.5px 1px rgba(0,0,0,0.1);
        }

        .header h2 {
            font-size: 13px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 6px;
        }

        .header-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 8px;
            color: #7f8c8d;
        }

        .reconciliation-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 6px;
            margin-bottom: 8px;
        }

        .reconciliation-info h3 {
            font-size: 10px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 4px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 2px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 5px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 2px 0;
            border-bottom: 1px dotted #bdc3c7;
            font-size: 8px;
        }

        .info-label {
            font-weight: 700;
            color: #1a252f;
        }

        .info-value {
            font-weight: 700;
            color: #2c3e50;
        }

        .section {
            margin-bottom: 6px;
            break-inside: avoid;
        }

        .section-title {
            font-size: 10px;
            font-weight: 700;
            color: #1a252f;
            background: #ecf0f1;
            padding: 4px 6px;
            border-radius: 2px;
            margin-bottom: 3px;
            border-right: 2px solid #3498db;
            text-shadow: 0.5px 0.5px 1px rgba(0,0,0,0.1);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 4px;
            font-size: 8px;
        }

        .table th {
            background: #34495e;
            color: white;
            padding: 3px 4px;
            text-align: center;
            font-weight: 700;
            border: 1px solid #2c3e50;
            text-shadow: 0.5px 0.5px 1px rgba(0,0,0,0.3);
        }

        .table td {
            padding: 3px 4px;
            text-align: center;
            border: 1px solid #bdc3c7;
            vertical-align: middle;
            line-height: 1.2;
            font-weight: 500;
        }

        .table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }

        .table tbody tr:hover {
            background: #e3f2fd;
        }

        .currency {
            font-weight: 800;
            color: #1e8449;
            text-align: left;
            direction: ltr;
            font-size: 1.05em;
            text-shadow: 0.5px 0.5px 1px rgba(0,0,0,0.1);
        }

        .summary {
            background: #e8f5e8;
            border: 1px solid #27ae60;
            border-radius: 3px;
            padding: 6px;
            margin-top: 8px;
        }

        .summary h3 {
            font-size: 11px;
            font-weight: 800;
            color: #1e8449;
            text-align: center;
            margin-bottom: 4px;
            text-shadow: 0.5px 0.5px 1px rgba(0,0,0,0.1);
        }

        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 3px;
        }

        .summary-item {
            text-align: center;
            padding: 3px;
            background: white;
            border-radius: 2px;
            border: 1px solid #27ae60;
        }

        .summary-label {
            font-size: 7.5px;
            color: #1a252f;
            margin-bottom: 2px;
            font-weight: 600;
        }

        .summary-value {
            font-size: 9px;
            font-weight: 800;
            color: #1e8449;
            text-shadow: 0.5px 0.5px 1px rgba(0,0,0,0.1);
        }

        .print-controls {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border: 1px solid #dee2e6;
        }

        .print-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            margin-left: 10px;
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            transition: background 0.3s ease;
        }

        .print-btn:hover {
            background: #2980b9;
        }

        .close-btn {
            background: #95a5a6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            transition: background 0.3s ease;
        }

        .close-btn:hover {
            background: #7f8c8d;
        }

        .empty-section {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px dashed #bdc3c7;
        }

        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #bdc3c7;
            text-align: center;
            color: #7f8c8d;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <!-- Print Controls -->
    <div class="print-controls no-print">
        <button class="print-btn" onclick="window.print()">🖨️ طباعة</button>
        <button class="close-btn" onclick="window.close()">✖️ إغلاق</button>
    </div>

    <!-- Header -->
    <div class="header">
        <h1>${reconciliation.company_name || 'نظام تصفية الكاشير'}</h1>
        <h2>تقرير التصفية النهائية</h2>
        <div class="header-info">
            <span>تاريخ الطباعة: ${currentDate}</span>
            <span>وقت الطباعة: ${currentTime}</span>
        </div>
    </div>

    <!-- Reconciliation Information -->
    <div class="reconciliation-info">
        <h3>معلومات التصفية</h3>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">رقم التصفية:</span>
                <span class="info-value">#${reconciliation.id}</span>
            </div>
            <div class="info-item">
                <span class="info-label">الفرع:</span>
                <span class="info-value">${reconciliation.branch_name || 'غير محدد'}</span>
            </div>
            <div class="info-item">
                <span class="info-label">اسم الكاشير:</span>
                <span class="info-value">${reconciliation.cashier_name || 'غير محدد'}</span>
            </div>
            <div class="info-item">
                <span class="info-label">رقم الكاشير:</span>
                <span class="info-value">${reconciliation.cashier_number || 'غير محدد'}</span>
            </div>
            <div class="info-item">
                <span class="info-label">اسم المحاسب:</span>
                <span class="info-value">${reconciliation.accountant_name || 'غير محدد'}</span>
            </div>
            <div class="info-item">
                <span class="info-label">تاريخ التصفية:</span>
                <span class="info-value">${formatDate(reconciliation.reconciliation_date)}</span>
            </div>
            <div class="info-item">
                <span class="info-label">حالة التصفية:</span>
                <span class="info-value">${reconciliation.status === 'completed' ? 'مكتملة' : 'مسودة'}</span>
            </div>
        </div>
    </div>
    ${generateSectionsHtml(sections)}
    ${generateSummaryHtml(reconciliation)}

    <!-- Footer -->
    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام تصفية الكاشير</p>
        <p>Cashier Reconciliation System - Generated on ${currentDate} at ${currentTime}</p>
    </div>

    <script>
        // Print functionality
        function printDocument() {
            window.print();
        }

        // Close window functionality
        function closeWindow() {
            window.close();
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                printDocument();
            }
            if (e.key === 'Escape') {
                closeWindow();
            }
        });

        console.log('🖨️ نافذة معاينة الطباعة جاهزة');
    </script>
</body>
</html>`;
}

// Generate sections HTML based on selected sections
function generateSectionsHtml(sections) {
  let html = '';

  if (!sections || typeof sections !== 'object') {
    return html;
  }

  // Bank Receipts Section
  if (sections.bankReceipts && sections.bankReceipts.length > 0) {
    html += `
    <div class="section">
        <div class="section-title">💳 المقبوضات البنكية</div>
        <table class="table">
            <thead>
                <tr>
                    <th>رقم المقبوضة</th>
                    <th>اسم الآلة</th>
                    <th>البنك</th>
                    <th>المبلغ</th>
                    <th>التاريخ</th>
                </tr>
            </thead>
            <tbody>`;

    sections.bankReceipts.forEach(receipt => {
      html += `
                <tr>
                    <td>${receipt.receipt_number || 'غير محدد'}</td>
                    <td>${receipt.atm_name || 'غير محدد'}</td>
                    <td>${receipt.bank_name || 'غير محدد'}</td>
                    <td class="currency">${formatCurrency(receipt.amount)} ريال</td>
                    <td>${receipt.created_at ? formatDate(receipt.created_at) : 'غير محدد'}</td>
                </tr>`;
    });

    const totalBankReceipts = sections.bankReceipts.reduce((sum, receipt) => sum + (receipt.amount || 0), 0);
    html += `
                <tr style="background: #e8f5e8; font-weight: bold;">
                    <td colspan="3">الإجمالي</td>
                    <td class="currency">${formatCurrency(totalBankReceipts)} ريال</td>
                    <td>${sections.bankReceipts.length} مقبوضة</td>
                </tr>
            </tbody>
        </table>
    </div>`;
  }

  // Cash Receipts Section - Fixed to show all denominations properly
  if (sections.cashReceipts && sections.cashReceipts.length > 0) {
    html += `
    <div class="section">
        <div class="section-title">💰 المقبوضات النقدية</div>
        <table class="table">
            <thead>
                <tr>
                    <th>الفئة</th>
                    <th>العدد</th>
                    <th>المبلغ الإجمالي</th>
                </tr>
            </thead>
            <tbody>`;

    // Sort by denomination descending for better readability
    const sortedCashReceipts = [...sections.cashReceipts].sort((a, b) => (b.denomination || 0) - (a.denomination || 0));

    sortedCashReceipts.forEach(receipt => {
      const denomination = formatNumber(receipt.denomination || 0);
      const quantity = formatNumber(receipt.quantity || 0);
      const totalAmount = formatNumber(formatCurrency(receipt.total_amount));

      html += `
                <tr>
                    <td>${denomination} ريال</td>
                    <td>${quantity}</td>
                    <td class="currency">${totalAmount} ريال</td>
                </tr>`;
    });

    const totalCashReceipts = sections.cashReceipts.reduce((sum, receipt) => sum + (receipt.total_amount || 0), 0);
    const totalQuantity = sections.cashReceipts.reduce((sum, receipt) => sum + (receipt.quantity || 0), 0);

    html += `
                <tr style="background: #e8f5e8; font-weight: bold;">
                    <td>الإجمالي</td>
                    <td>${formatNumber(totalQuantity)}</td>
                    <td class="currency">${formatNumber(formatCurrency(totalCashReceipts))} ريال</td>
                </tr>
            </tbody>
        </table>
    </div>`;
  }

  // Postpaid Sales Section
  if (sections.postpaidSales && sections.postpaidSales.length > 0) {
    html += `
    <div class="section">
        <div class="section-title">📱 المبيعات الآجلة</div>
        <table class="table">
            <thead>
                <tr>
                    <th>رقم الفاتورة</th>
                    <th>اسم العميل</th>
                    <th>رقم الهاتف</th>
                    <th>المبلغ</th>
                    <th>التاريخ</th>
                </tr>
            </thead>
            <tbody>`;

    sections.postpaidSales.forEach(sale => {
      html += `
                <tr>
                    <td>${sale.invoice_number || 'غير محدد'}</td>
                    <td>${sale.customer_name || 'غير محدد'}</td>
                    <td>${sale.phone_number || 'غير محدد'}</td>
                    <td class="currency">${formatCurrency(sale.amount)} ريال</td>
                    <td>${sale.created_at ? formatDate(sale.created_at) : 'غير محدد'}</td>
                </tr>`;
    });

    const totalPostpaidSales = sections.postpaidSales.reduce((sum, sale) => sum + (sale.amount || 0), 0);
    html += `
                <tr style="background: #e8f5e8; font-weight: bold;">
                    <td colspan="3">الإجمالي</td>
                    <td class="currency">${formatCurrency(totalPostpaidSales)} ريال</td>
                    <td>${sections.postpaidSales.length} فاتورة</td>
                </tr>
            </tbody>
        </table>
    </div>`;
  }

  // Customer Receipts Section
  if (sections.customerReceipts && sections.customerReceipts.length > 0) {
    html += `
    <div class="section">
        <div class="section-title">👥 مقبوضات العملاء</div>
        <table class="table">
            <thead>
                <tr>
                    <th>اسم العميل</th>
                    <th>رقم الهاتف</th>
                    <th>المبلغ</th>
                    <th>الملاحظات</th>
                    <th>التاريخ</th>
                </tr>
            </thead>
            <tbody>`;

    sections.customerReceipts.forEach(receipt => {
      html += `
                <tr>
                    <td>${receipt.customer_name || 'غير محدد'}</td>
                    <td>${receipt.phone_number || 'غير محدد'}</td>
                    <td class="currency">${formatCurrency(receipt.amount)} ريال</td>
                    <td>${receipt.notes || 'لا توجد ملاحظات'}</td>
                    <td>${receipt.created_at ? formatDate(receipt.created_at) : 'غير محدد'}</td>
                </tr>`;
    });

    const totalCustomerReceipts = sections.customerReceipts.reduce((sum, receipt) => sum + (receipt.amount || 0), 0);
    html += `
                <tr style="background: #e8f5e8; font-weight: bold;">
                    <td colspan="2">الإجمالي</td>
                    <td class="currency">${formatCurrency(totalCustomerReceipts)} ريال</td>
                    <td colspan="2">${sections.customerReceipts.length} مقبوضة</td>
                </tr>
            </tbody>
        </table>
    </div>`;
  }

  // Return Invoices Section
  if (sections.returnInvoices && sections.returnInvoices.length > 0) {
    html += `
    <div class="section">
        <div class="section-title">↩️ فواتير المرتجع</div>
        <table class="table">
            <thead>
                <tr>
                    <th>رقم الفاتورة</th>
                    <th>اسم العميل</th>
                    <th>المبلغ</th>
                    <th>السبب</th>
                    <th>التاريخ</th>
                </tr>
            </thead>
            <tbody>`;

    sections.returnInvoices.forEach(invoice => {
      html += `
                <tr>
                    <td>${invoice.invoice_number || 'غير محدد'}</td>
                    <td>${invoice.customer_name || 'غير محدد'}</td>
                    <td class="currency">${formatCurrency(invoice.amount)} ريال</td>
                    <td>${invoice.reason || 'غير محدد'}</td>
                    <td>${invoice.created_at ? formatDate(invoice.created_at) : 'غير محدد'}</td>
                </tr>`;
    });

    const totalReturnInvoices = sections.returnInvoices.reduce((sum, invoice) => sum + (invoice.amount || 0), 0);
    html += `
                <tr style="background: #ffe8e8; font-weight: bold;">
                    <td colspan="2">الإجمالي</td>
                    <td class="currency">${formatCurrency(totalReturnInvoices)} ريال</td>
                    <td colspan="2">${sections.returnInvoices.length} فاتورة</td>
                </tr>
            </tbody>
        </table>
    </div>`;
  }

  // Suppliers Section
  if (sections.suppliers && sections.suppliers.length > 0) {
    html += `
    <div class="section">
        <div class="section-title">🏪 الموردين</div>
        <table class="table">
            <thead>
                <tr>
                    <th>اسم المورد</th>
                    <th>رقم الهاتف</th>
                    <th>المبلغ</th>
                    <th>الملاحظات</th>
                    <th>التاريخ</th>
                </tr>
            </thead>
            <tbody>`;

    sections.suppliers.forEach(supplier => {
      html += `
                <tr>
                    <td>${supplier.supplier_name || 'غير محدد'}</td>
                    <td>${supplier.phone_number || 'غير محدد'}</td>
                    <td class="currency">${formatCurrency(supplier.amount)} ريال</td>
                    <td>${supplier.notes || 'لا توجد ملاحظات'}</td>
                    <td>${supplier.created_at ? formatDate(supplier.created_at) : 'غير محدد'}</td>
                </tr>`;
    });

    const totalSuppliers = sections.suppliers.reduce((sum, supplier) => sum + (supplier.amount || 0), 0);
    html += `
                <tr style="background: #e8f5e8; font-weight: bold;">
                    <td colspan="2">الإجمالي</td>
                    <td class="currency">${formatCurrency(totalSuppliers)} ريال</td>
                    <td colspan="2">${sections.suppliers.length} مورد</td>
                </tr>
            </tbody>
        </table>
    </div>`;
  }

  return html;
}

// Generate summary HTML
function generateSummaryHtml(reconciliation) {
  const systemSales = reconciliation.system_sales || 0;
  const totalReceipts = reconciliation.total_receipts || 0;
  const surplusDeficit = reconciliation.surplus_deficit || 0;

  const surplusDeficitClass = surplusDeficit > 0 ? 'color: #27ae60' : surplusDeficit < 0 ? 'color: #e74c3c' : 'color: #7f8c8d';
  const surplusDeficitText = surplusDeficit > 0 ? 'فائض' : surplusDeficit < 0 ? 'عجز' : 'متوازن';

  return `
    <div class="summary">
        <h3>ملخص التصفية النهائية</h3>
        <div class="summary-grid">
            <div class="summary-item">
                <div class="summary-label">مبيعات النظام</div>
                <div class="summary-value">${formatCurrency(systemSales)} ريال</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">إجمالي المقبوضات</div>
                <div class="summary-value">${formatCurrency(totalReceipts)} ريال</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">${surplusDeficitText}</div>
                <div class="summary-value" style="${surplusDeficitClass}">${formatCurrency(Math.abs(surplusDeficit))} ريال</div>
            </div>
        </div>
    </div>`;
}

function initializeDatabase() {
  try {
    dbManager = new DatabaseManager();
    const success = dbManager.initialize();

    if (!success) {
      console.error('Failed to initialize database');
      return false;
    }

    console.log('Database manager initialized successfully');
    return true;

  } catch (error) {
    console.error('Database initialization error:', error);
    return false;
  }
}



// App event handlers
app.whenReady().then(() => {
  const dbInitialized = initializeDatabase();
  if (dbInitialized) {
    // Initialize PDF generator
    pdfGenerator = new PDFGenerator(dbManager);

    // Initialize Print manager
    printManager = new PrintManager();
    printManager.initialize();

    // Initialize Mobile Server
    mobileServer = new MobileServer(3000);
    mobileServer.start().then((port) => {
      console.log(`🌐 [MAIN] خادم الهاتف المحمول يعمل على المنفذ ${port}`);
    }).catch((error) => {
      console.warn('⚠️ [MAIN] تعذر بدء خادم الهاتف المحمول:', error.message);
    });

    createWindow();
  } else {
    console.error('Failed to initialize database, exiting...');
    app.quit();
  }

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    // Clean up resources properly
    try {
      if (dbManager) {
        console.log('🔄 Closing database connection...');
        dbManager.close();
        dbManager = null;
      }
      if (pdfGenerator) {
        console.log('🔄 Closing PDF generator...');
        pdfGenerator.close();
        pdfGenerator = null;
      }
      if (printManager) {
        console.log('🔄 Cleaning up print manager...');
        printManager = null;
      }
      if (printPreviewWindow && !printPreviewWindow.isDestroyed()) {
        printPreviewWindow.close();
        printPreviewWindow = null;
      }
      if (mobileServer) {
        console.log('🔄 إيقاف خادم الهاتف المحمول...');
        mobileServer.stop();
        mobileServer = null;
      }
      console.log('✅ All resources cleaned up successfully');
    } catch (error) {
      console.error('❌ Error during cleanup:', error);
    }
    app.quit();
  }
});

// IPC handlers for database operations
ipcMain.handle('db-query', async (event, sql, params = []) => {
  try {
    if (!dbManager || !dbManager.db) {
      throw new Error('Database not initialized');
    }
    return dbManager.query(sql, params);
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
});

ipcMain.handle('db-run', async (event, sql, params = []) => {
  try {
    if (!dbManager || !dbManager.db) {
      throw new Error('Database not initialized');
    }
    return dbManager.run(sql, params);
  } catch (error) {
    console.error('Database run error:', error);
    throw error;
  }
});

ipcMain.handle('db-get', async (event, sql, params = []) => {
  try {
    if (!dbManager || !dbManager.db) {
      throw new Error('Database not initialized');
    }
    return dbManager.get(sql, params);
  } catch (error) {
    console.error('Database get error:', error);
    throw error;
  }
});

ipcMain.handle('db-all', async (event, sql, params = []) => {
  try {
    if (!dbManager || !dbManager.db) {
      throw new Error('Database not initialized');
    }
    return dbManager.query(sql, params);
  } catch (error) {
    console.error('Database all error:', error);
    throw error;
  }
});

// Get reconciliation for editing
ipcMain.handle('get-reconciliation-for-edit', async (event, reconciliationId) => {
  console.log('🔍 [IPC] طلب تحميل التصفية للتعديل - معرف:', reconciliationId, 'نوع:', typeof reconciliationId);

  try {
    // Validate input
    if (reconciliationId === null || reconciliationId === undefined) {
      console.error('❌ [IPC] معرف التصفية مفقود');
      throw new Error('معرف التصفية مطلوب');
    }

    // Check database manager
    if (!dbManager) {
      console.error('❌ [IPC] مدير قاعدة البيانات غير مهيأ');
      throw new Error('مدير قاعدة البيانات غير مهيأ');
    }

    if (!dbManager.db) {
      console.error('❌ [IPC] قاعدة البيانات غير متصلة');
      throw new Error('قاعدة البيانات غير متصلة');
    }

    console.log('✅ [IPC] قاعدة البيانات متاحة، بدء التحميل...');

    const startTime = Date.now();
    const result = dbManager.getReconciliationForEdit(reconciliationId);
    const loadTime = Date.now() - startTime;

    console.log(`⏱️ [IPC] وقت تحميل البيانات: ${loadTime}ms`);

    if (!result) {
      console.error('❌ [IPC] لم يتم إرجاع بيانات من قاعدة البيانات');
      throw new Error(`لم يتم العثور على التصفية رقم ${reconciliationId}`);
    }

    console.log('✅ [IPC] تم تحميل البيانات بنجاح');
    return result;

  } catch (error) {
    console.error('❌ [IPC] خطأ في تحميل التصفية للتعديل:', {
      reconciliationId: reconciliationId,
      error: error.message,
      stack: error.stack
    });

    // Re-throw with more context
    const enhancedError = new Error(`فشل في تحميل التصفية: ${error.message}`);
    enhancedError.originalError = error;
    enhancedError.reconciliationId = reconciliationId;
    throw enhancedError;
  }
});

// Update reconciliation with modification date
ipcMain.handle('update-reconciliation-modified', async (event, reconciliationId, systemSales, totalReceipts, surplusDeficit, status) => {
  try {
    if (!dbManager || !dbManager.db) {
      throw new Error('Database not initialized');
    }
    return dbManager.updateReconciliationModified(reconciliationId, systemSales, totalReceipts, surplusDeficit, status);
  } catch (error) {
    console.error('Error updating reconciliation:', error);
    throw error;
  }
});

// PDF generation handler
ipcMain.handle('generate-pdf', async (event, reconciliationData) => {
  try {
    if (!pdfGenerator) {
      throw new Error('PDF generator not initialized');
    }

    const pdfBuffer = await pdfGenerator.generateReconciliationReport(reconciliationData);

    // Get default save path from settings
    let defaultPath = `تقرير_تصفية_${reconciliationData.cashierName}_${reconciliationData.reconciliationDate}.pdf`;
    try {
      const savedPath = await dbManager.get(
        'SELECT setting_value FROM system_settings WHERE category = ? AND setting_key = ?',
        ['reports', 'default_save_path']
      );
      if (savedPath && savedPath.setting_value) {
        const path = require('path');
        defaultPath = path.join(savedPath.setting_value, defaultPath);
      }
    } catch (error) {
      console.log('ℹ️ [IPC] لم يتم العثور على مسار افتراضي محفوظ');
    }

    // Show save dialog
    const result = await dialog.showSaveDialog(mainWindow, {
      title: 'حفظ تقرير التصفية',
      defaultPath: defaultPath,
      filters: [
        { name: 'PDF Files', extensions: ['pdf'] }
      ]
    });

    if (!result.canceled && result.filePath) {
      fs.writeFileSync(result.filePath, pdfBuffer);
      return { success: true, filePath: result.filePath };
    } else {
      return { success: false, message: 'تم إلغاء العملية' };
    }

  } catch (error) {
    console.error('PDF generation error:', error);
    return { success: false, message: error.message };
  }
});

// Advanced printing handlers
ipcMain.handle('get-printers', async () => {
  try {
    if (!printManager) {
      throw new Error('Print manager not initialized');
    }

    // Use main window to get printers
    if (mainWindow && mainWindow.webContents) {
      try {
        const printers = await mainWindow.webContents.getPrinters();
        return printers.map(printer => ({
          name: printer.name,
          displayName: printer.displayName || printer.name,
          description: printer.description || '',
          status: printer.status || 'unknown',
          isDefault: printer.isDefault || false
        }));
      } catch (printerError) {
        console.warn('Could not get system printers, using fallback');
        return [{
          name: 'default',
          displayName: 'الطابعة الافتراضية',
          description: 'طابعة النظام الافتراضية',
          status: 'available',
          isDefault: true
        }];
      }
    } else {
      return [{
        name: 'default',
        displayName: 'الطابعة الافتراضية',
        description: 'طابعة النظام الافتراضية',
        status: 'available',
        isDefault: true
      }];
    }
  } catch (error) {
    console.error('Error getting printers:', error);
    return [{
      name: 'default',
      displayName: 'الطابعة الافتراضية',
      description: 'طابعة النظام الافتراضية',
      status: 'available',
      isDefault: true
    }];
  }
});

ipcMain.handle('get-print-settings', async () => {
  try {
    if (!printManager) {
      throw new Error('Print manager not initialized');
    }
    return printManager.getPrintSettings();
  } catch (error) {
    console.error('Error getting print settings:', error);
    throw error;
  }
});

ipcMain.handle('update-print-settings', async (event, settings) => {
  try {
    if (!printManager) {
      throw new Error('Print manager not initialized');
    }
    printManager.updatePrintSettings(settings);
    return { success: true };
  } catch (error) {
    console.error('Error updating print settings:', error);
    throw error;
  }
});

ipcMain.handle('print-direct', async (event, reconciliationData, printOptions = {}) => {
  try {
    if (!printManager) {
      throw new Error('Print manager not initialized');
    }

    const htmlContent = await printManager.generateReconciliationPrintHTML(reconciliationData);
    return await printManager.printHTML(htmlContent, printOptions);
  } catch (error) {
    console.error('Direct print error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('print-preview', async (event, reconciliationData, printOptions = {}) => {
  try {
    if (!printManager) {
      throw new Error('Print manager not initialized');
    }

    const htmlContent = await printManager.generateReconciliationPrintHTML(reconciliationData);
    return await printManager.printWithPreview(htmlContent, printOptions);
  } catch (error) {
    console.error('Print preview error:', error);
    return { success: false, error: error.message };
  }
});

// Export PDF handler for reports
ipcMain.handle('export-pdf', async (event, exportData) => {
  console.log('📄 [IPC] طلب تصدير PDF...');

  try {
    if (!exportData) {
      throw new Error('بيانات التصدير مطلوبة');
    }

    if (!exportData.html) {
      throw new Error('محتوى HTML مطلوب للتصدير');
    }

    if (!pdfGenerator) {
      throw new Error('PDF generator not initialized');
    }

    // Generate PDF from HTML
    const pdfBuffer = await pdfGenerator.generateFromHTML(exportData.html);

    // Get default save path from settings
    let defaultPath = exportData.filename || `report-${new Date().toISOString().split('T')[0]}.pdf`;
    try {
      const savedPath = await dbManager.get(
        'SELECT setting_value FROM system_settings WHERE category = ? AND setting_key = ?',
        ['reports', 'default_save_path']
      );
      if (savedPath && savedPath.setting_value) {
        const path = require('path');
        defaultPath = path.join(savedPath.setting_value, defaultPath);
      }
    } catch (error) {
      console.log('ℹ️ [IPC] لم يتم العثور على مسار افتراضي محفوظ');
    }

    // Show save dialog
    const result = await dialog.showSaveDialog(mainWindow, {
      title: 'حفظ تقرير PDF',
      defaultPath: defaultPath,
      filters: [
        { name: 'PDF Files', extensions: ['pdf'] }
      ]
    });

    if (!result.canceled && result.filePath) {
      fs.writeFileSync(result.filePath, pdfBuffer);
      console.log('✅ [IPC] تم تصدير PDF بنجاح:', result.filePath);
      return { success: true, filePath: result.filePath };
    } else {
      return { success: false, error: 'تم إلغاء العملية' };
    }

  } catch (error) {
    console.error('❌ [IPC] خطأ في تصدير PDF:', error);
    return { success: false, error: error.message };
  }
});

// Export Excel handler for reports
ipcMain.handle('export-excel', async (event, exportData) => {
  console.log('📊 [IPC] طلب تصدير Excel...');

  try {
    if (!exportData) {
      throw new Error('بيانات التصدير مطلوبة');
    }

    if (!exportData.data) {
      throw new Error('بيانات Excel مطلوبة للتصدير');
    }

    // Create Excel workbook
    const ExcelJS = require('exceljs');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('تقرير التصفيات');

    // Add headers
    if (exportData.data.headers) {
      worksheet.addRow(exportData.data.headers);

      // Style headers
      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };
    }

    // Add data rows
    if (exportData.data.rows) {
      exportData.data.rows.forEach(row => {
        worksheet.addRow(row);
      });
    }

    // Auto-fit columns
    worksheet.columns.forEach(column => {
      column.width = 15;
    });

    // Get default save path from settings
    let defaultPath = exportData.filename || `report-${new Date().toISOString().split('T')[0]}.xlsx`;
    try {
      const savedPath = await dbManager.get(
        'SELECT setting_value FROM system_settings WHERE category = ? AND setting_key = ?',
        ['reports', 'default_save_path']
      );
      if (savedPath && savedPath.setting_value) {
        const path = require('path');
        defaultPath = path.join(savedPath.setting_value, defaultPath);
      }
    } catch (error) {
      console.log('ℹ️ [IPC] لم يتم العثور على مسار افتراضي محفوظ');
    }

    // Show save dialog
    const result = await dialog.showSaveDialog(mainWindow, {
      title: 'حفظ تقرير Excel',
      defaultPath: defaultPath,
      filters: [
        { name: 'Excel Files', extensions: ['xlsx'] }
      ]
    });

    if (!result.canceled && result.filePath) {
      await workbook.xlsx.writeFile(result.filePath);
      console.log('✅ [IPC] تم تصدير Excel بنجاح:', result.filePath);
      return { success: true, filePath: result.filePath };
    } else {
      return { success: false, error: 'تم إلغاء العملية' };
    }

  } catch (error) {
    console.error('❌ [IPC] خطأ في تصدير Excel:', error);
    return { success: false, error: error.message };
  }
});

// Create print preview window with Arabic support
ipcMain.handle('create-print-preview', async (event, printData) => {
  console.log('🖨️ [IPC] طلب إنشاء نافذة معاينة الطباعة...');

  try {
    // Validate print data
    if (!printData) {
      throw new Error('بيانات الطباعة مطلوبة');
    }

    // Handle different data formats
    if (printData.html && printData.title) {
      // Report HTML format
      console.log('📄 [IPC] معاينة طباعة تقرير HTML');
      const previewWindow = createReportPrintPreviewWindow(printData);

      if (previewWindow) {
        console.log('✅ [IPC] تم إنشاء نافذة معاينة طباعة التقرير بنجاح');
        return { success: true, windowId: previewWindow.id };
      } else {
        throw new Error('فشل في إنشاء نافذة معاينة طباعة التقرير');
      }
    } else if (printData.reconciliation) {
      // Reconciliation format
      console.log('📊 [IPC] معاينة طباعة التصفية');
      console.log('📊 [IPC] بيانات الطباعة:', {
        reconciliationId: printData.reconciliation.id,
        sectionsCount: Object.keys(printData.sections || {}).length,
        hasOptions: !!printData.options
      });

      // Create print preview window
      const previewWindow = createPrintPreviewWindow(printData);

      if (previewWindow) {
        console.log('✅ [IPC] تم إنشاء نافذة معاينة الطباعة بنجاح');
        return { success: true, windowId: previewWindow.id };
      } else {
        throw new Error('فشل في إنشاء نافذة معاينة الطباعة');
      }
    } else {
      throw new Error('تنسيق بيانات الطباعة غير مدعوم');
    }

  } catch (error) {
    console.error('❌ [IPC] خطأ في إنشاء نافذة معاينة الطباعة:', error);
    return { success: false, error: error.message };
  }
});

// Helper function to create report print preview window
function createReportPrintPreviewWindow(printData) {
  try {
    console.log('🖨️ [HELPER] إنشاء نافذة معاينة طباعة التقرير...');

    // Create print preview window
    printPreviewWindow = new BrowserWindow({
      width: 900,
      height: 1200,
      minWidth: 800,
      minHeight: 1000,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        enableRemoteModule: true
      },
      title: printData.title || 'معاينة الطباعة',
      icon: path.join(__dirname, '../assets/icon.png'),
      parent: mainWindow,
      modal: false,
      show: false,
      autoHideMenuBar: true,
      webSecurity: false
    });

    // Create HTML content with print styles
    const htmlContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${printData.title || 'معاينة الطباعة'}</title>
        <style>
          @media print {
            body { margin: 0; margin-bottom: 25mm; }
            .no-print { display: none !important; }
            .page-footer {
              position: fixed;
              bottom: 0;
              left: 0;
              right: 0;
              height: 20mm;
              background: white;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 10px;
              color: #666;
              border-top: 1px solid #ddd;
              z-index: 1000;
            }
          }
          @page {
            margin-bottom: 25mm;
          }
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background: white;
            margin-bottom: 25mm;
          }
          .print-controls {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 1000;
            background: white;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
          }
          .print-controls button {
            margin: 0 5px;
            padding: 8px 15px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
          }
          .print-btn { background: #007bff; color: white; }
          .close-btn { background: #6c757d; color: white; }
        </style>
      </head>
      <body>
        <div class="print-controls no-print">
          <button class="print-btn" onclick="window.print()">🖨️ طباعة</button>
          <button class="close-btn" onclick="window.close()">❌ إغلاق</button>
        </div>
        ${printData.html}

        <!-- فوتر الصفحة - يظهر في كل صفحة مطبوعة -->
        <div class="page-footer">
          جميع الحقوق محفوظة © 2025 - تطوير محمد أمين الكامل - نظام تصفية برو
        </div>
      </body>
      </html>
    `;

    // Load HTML content
    printPreviewWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`);

    // Show window when ready
    printPreviewWindow.once('ready-to-show', () => {
      printPreviewWindow.show();
      console.log('✅ [HELPER] تم عرض نافذة معاينة طباعة التقرير');
    });

    // Handle window closed
    printPreviewWindow.on('closed', () => {
      printPreviewWindow = null;
      console.log('🖨️ [HELPER] تم إغلاق نافذة معاينة طباعة التقرير');
    });

    return printPreviewWindow;

  } catch (error) {
    console.error('❌ [HELPER] خطأ في إنشاء نافذة معاينة طباعة التقرير:', error);
    return null;
  }
}

// Close print preview window
ipcMain.handle('close-print-preview', async (event) => {
  console.log('🖨️ [IPC] طلب إغلاق نافذة معاينة الطباعة...');

  try {
    if (printPreviewWindow && !printPreviewWindow.isDestroyed()) {
      printPreviewWindow.close();
      printPreviewWindow = null;
      console.log('✅ [IPC] تم إغلاق نافذة معاينة الطباعة');
      return { success: true };
    } else {
      console.log('⚠️ [IPC] نافذة معاينة الطباعة غير موجودة');
      return { success: true, message: 'نافذة معاينة الطباعة غير موجودة' };
    }
  } catch (error) {
    console.error('❌ [IPC] خطأ في إغلاق نافذة معاينة الطباعة:', error);
    return { success: false, error: error.message };
  }
});

// Get system information
ipcMain.handle('get-system-info', async (event) => {
  try {
    const os = require('os');
    const process = require('process');

    const systemInfo = {
      nodeVersion: process.version,
      electronVersion: process.versions.electron,
      osInfo: `${os.type()} ${os.release()} ${os.arch()}`,
      memoryUsage: `${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)} MB`,
      uptime: formatUptime(process.uptime())
    };

    return systemInfo;
  } catch (error) {
    console.error('❌ [IPC] خطأ في الحصول على معلومات النظام:', error);
    return null;
  }
});

// Get database statistics
ipcMain.handle('get-database-stats', async (event) => {
  try {
    if (!dbManager || !dbManager.db) {
      throw new Error('Database not initialized');
    }

    // Get database file size
    const fs = require('fs');
    const path = require('path');
    const dbPath = path.join(app.getPath('userData'), 'casher.db');

    let size = 'غير متاح';
    let recordCount = 0;

    try {
      const stats = fs.statSync(dbPath);
      size = `${formatCurrency(stats.size / 1024 / 1024)} MB`;
    } catch (error) {
      console.error('خطأ في قراءة حجم قاعدة البيانات:', error);
    }

    // Get total record count
    try {
      const tables = ['reconciliations', 'bank_receipts', 'cash_receipts', 'customer_receipts', 'postpaid_sales', 'return_invoices', 'suppliers'];
      for (const table of tables) {
        const result = dbManager.db.prepare(`SELECT COUNT(*) as count FROM ${table}`).get();
        recordCount += result.count;
      }
    } catch (error) {
      console.error('خطأ في حساب عدد السجلات:', error);
    }

    return {
      size: size,
      recordCount: formatNumber(recordCount)
    };

  } catch (error) {
    console.error('❌ [IPC] خطأ في الحصول على إحصائيات قاعدة البيانات:', error);
    return null;
  }
});

// Helper function to format uptime
function formatUptime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours} ساعة ${minutes} دقيقة`;
  } else if (minutes > 0) {
    return `${minutes} دقيقة ${secs} ثانية`;
  } else {
    return `${secs} ثانية`;
  }
}

// ========================================
// BACKUP AND RESTORE IPC HANDLERS
// ========================================

// Show save dialog for backup
ipcMain.handle('show-save-dialog', async (event, options) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, options);
    return result.canceled ? null : result.filePath;
  } catch (error) {
    console.error('❌ [IPC] خطأ في عرض حوار الحفظ:', error);
    throw error;
  }
});

// Show open dialog for restore
ipcMain.handle('show-open-dialog', async (event, options) => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, options);
    return result.canceled ? [] : result.filePaths;
  } catch (error) {
    console.error('❌ [IPC] خطأ في عرض حوار الفتح:', error);
    throw error;
  }
});

// Save backup file
ipcMain.handle('save-backup-file', async (event, { filePath, data }) => {
  try {
    console.log('💾 [IPC] حفظ ملف النسخة الاحتياطية:', filePath);

    const jsonData = JSON.stringify(data, null, 2);
    fs.writeFileSync(filePath, jsonData, 'utf8');

    const stats = fs.statSync(filePath);
    const fileSize = `${formatCurrency(stats.size / 1024 / 1024)} MB`;
    const recordCount = data.metadata.total_records || 0;

    console.log('✅ [IPC] تم حفظ النسخة الاحتياطية بنجاح');
    return {
      success: true,
      fileSize: fileSize,
      recordCount: recordCount
    };

  } catch (error) {
    console.error('❌ [IPC] خطأ في حفظ النسخة الاحتياطية:', error);
    return { success: false, error: error.message };
  }
});

// Select directory dialog
ipcMain.handle('select-directory', async (event, options = {}) => {
  try {
    console.log('📁 [IPC] فتح حوار اختيار المجلد...');

    const result = await dialog.showOpenDialog(mainWindow, {
      title: options.title || 'اختر مجلد',
      defaultPath: options.defaultPath || '',
      properties: ['openDirectory', 'createDirectory']
    });

    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      console.log('✅ [IPC] تم اختيار المجلد:', result.filePaths[0]);
      return { success: true, filePath: result.filePaths[0] };
    } else {
      return { success: false, message: 'تم إلغاء العملية' };
    }

  } catch (error) {
    console.error('❌ [IPC] خطأ في اختيار المجلد:', error);
    return { success: false, error: error.message };
  }
});

// Load backup file
ipcMain.handle('load-backup-file', async (event, filePath) => {
  try {
    console.log('📥 [IPC] تحميل ملف النسخة الاحتياطية:', filePath);

    if (!fs.existsSync(filePath)) {
      throw new Error('ملف النسخة الاحتياطية غير موجود');
    }

    const jsonData = fs.readFileSync(filePath, 'utf8');
    const backupData = JSON.parse(jsonData);

    console.log('✅ [IPC] تم تحميل النسخة الاحتياطية بنجاح');
    return { success: true, data: backupData };

  } catch (error) {
    console.error('❌ [IPC] خطأ في تحميل النسخة الاحتياطية:', error);
    return { success: false, error: error.message };
  }
});

// Helper function to format numbers using English digits
function formatNumber(number) {
  if (number === null || number === undefined) return '0';

  try {
    return new Intl.NumberFormat('en-US').format(number);
  } catch (error) {
    console.error('Error formatting number:', error);
    return String(number);
  }
}

// ===================================================
// QR Code IPC Handlers
// ===================================================

// Save QR Code settings
ipcMain.handle('save-qr-settings', async (event, settings) => {
  try {
    console.log('💾 [IPC] حفظ إعدادات QR Code...');

    if (!dbManager) {
      throw new Error('Database manager not initialized');
    }

    // حفظ كل إعداد في قاعدة البيانات
    const settingsToSave = [
      { key: 'qr.enabled', value: settings.enabled ? '1' : '0' },
      { key: 'qr.position', value: settings.position },
      { key: 'qr.size', value: settings.size.toString() },
      { key: 'qr.margin', value: settings.margin.toString() },
      { key: 'qr.baseUrl', value: settings.baseUrl },
      { key: 'qr.errorLevel', value: settings.errorLevel }
    ];

    for (const setting of settingsToSave) {
      dbManager.query(`
        INSERT OR REPLACE INTO settings (key, value, updated_at)
        VALUES (?, ?, datetime('now'))
      `, [setting.key, setting.value]);
    }

    // تحديث إعدادات print manager
    if (printManager) {
      printManager.updateQRSettings({
        enabled: settings.enabled,
        position: settings.position,
        size: settings.size,
        margin: settings.margin,
        baseUrl: settings.baseUrl
      });
    }

    console.log('✅ [IPC] تم حفظ إعدادات QR Code بنجاح');
    return { success: true };

  } catch (error) {
    console.error('❌ [IPC] خطأ في حفظ إعدادات QR Code:', error);
    return { success: false, message: error.message };
  }
});

// Get QR Code settings
ipcMain.handle('get-qr-settings', async (event) => {
  try {
    console.log('📥 [IPC] تحميل إعدادات QR Code...');

    if (!dbManager) {
      throw new Error('Database manager not initialized');
    }

    const settings = {};
    const settingsKeys = [
      'qr.enabled',
      'qr.position',
      'qr.size',
      'qr.margin',
      'qr.baseUrl',
      'qr.errorLevel'
    ];

    for (const key of settingsKeys) {
      const result = dbManager.query('SELECT value FROM settings WHERE key = ?', [key]);
      if (result && result.length > 0) {
        const shortKey = key.replace('qr.', '');
        let value = result[0].value;

        // تحويل القيم حسب النوع
        if (shortKey === 'enabled') {
          settings[shortKey] = value === '1';
        } else if (shortKey === 'size' || shortKey === 'margin') {
          settings[shortKey] = parseInt(value);
        } else {
          settings[shortKey] = value;
        }
      }
    }

    // القيم الافتراضية
    const defaultSettings = {
      enabled: true,
      position: 'bottom-right',
      size: 120,
      margin: 10,
      baseUrl: 'http://localhost:3000',
      errorLevel: 'M'
    };

    const finalSettings = { ...defaultSettings, ...settings };

    console.log('✅ [IPC] تم تحميل إعدادات QR Code بنجاح');
    return { success: true, settings: finalSettings };

  } catch (error) {
    console.error('❌ [IPC] خطأ في تحميل إعدادات QR Code:', error);
    return { success: false, message: error.message };
  }
});

// Generate test QR Code
ipcMain.handle('generate-test-qr', async (event, testData) => {
  try {
    console.log('🔲 [IPC] إنشاء QR Code تجريبي...');

    if (!printManager || !printManager.qrGenerator) {
      throw new Error('QR Generator not initialized');
    }

    const testUrl = `http://localhost:3000/mobile-viewer.html?data=${encodeURIComponent(JSON.stringify(testData))}`;
    const qrCodeDataUrl = await printManager.qrGenerator.generateTestQR(testUrl);

    console.log('✅ [IPC] تم إنشاء QR Code تجريبي بنجاح');
    return { success: true, qrCode: qrCodeDataUrl };

  } catch (error) {
    console.error('❌ [IPC] خطأ في إنشاء QR Code تجريبي:', error);
    return { success: false, message: error.message };
  }
});

// Test QR Code generation
ipcMain.handle('test-qr-generation', async (event) => {
  try {
    console.log('🧪 [IPC] اختبار إنشاء QR Code...');

    const startTime = Date.now();

    if (!printManager || !printManager.qrGenerator) {
      throw new Error('QR Generator not initialized');
    }

    // إنشاء بيانات تجريبية
    const testData = {
      type: 'reconciliation',
      id: 'TEST-' + Date.now(),
      cashier_name: 'اختبار الكاشير',
      reconciliation_date: new Date().toISOString().split('T')[0],
      summary: {
        total_receipts: 1000,
        system_sales: 950,
        surplus_deficit: 50
      }
    };

    const qrResult = await printManager.generateReconciliationQR(testData);
    const endTime = Date.now();

    if (qrResult && qrResult.qrCode) {
      console.log('✅ [IPC] اختبار إنشاء QR Code ناجح');
      return {
        success: true,
        time: endTime - startTime,
        dataSize: qrResult.data ? qrResult.data.length : 0
      };
    } else {
      throw new Error('فشل في إنشاء QR Code');
    }

  } catch (error) {
    console.error('❌ [IPC] خطأ في اختبار إنشاء QR Code:', error);
    return { success: false, message: error.message };
  }
});

// Test QR Code decoding
ipcMain.handle('test-qr-decoding', async (event) => {
  try {
    console.log('🧪 [IPC] اختبار فك تشفير QR Code...');

    const startTime = Date.now();

    if (!printManager || !printManager.qrGenerator) {
      throw new Error('QR Generator not initialized');
    }

    // إنشاء بيانات تجريبية وتشفيرها
    const testData = {
      type: 'report',
      title: 'تقرير اختبار',
      reconciliationsCount: 5,
      totalAmount: 25000
    };

    const dataManager = printManager.qrGenerator.dataManager;
    const encodedData = dataManager.encodeReportData(testData, {});
    const decodedData = dataManager.decodeData(encodedData);

    const endTime = Date.now();

    if (decodedData && decodedData.type) {
      console.log('✅ [IPC] اختبار فك تشفير QR Code ناجح');
      return {
        success: true,
        time: endTime - startTime,
        dataType: decodedData.type
      };
    } else {
      throw new Error('فشل في فك تشفير البيانات');
    }

  } catch (error) {
    console.error('❌ [IPC] خطأ في اختبار فك تشفير QR Code:', error);
    return { success: false, message: error.message };
  }
});
