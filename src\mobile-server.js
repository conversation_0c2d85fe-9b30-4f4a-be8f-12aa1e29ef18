// ===================================================
// 🧾 تطبيق: تصفية برو
// 🛠️ المطور: محمد أمين الكامل
// 🗓️ سنة: 2025
// 📌 جميع الحقوق محفوظة
// يمنع الاستخدام أو التعديل دون إذن كتابي
// ===================================================

/**
 * Mobile Server - خادم الواجهة المتجاوبة
 * خادم ويب بسيط لعرض التصفيات والتقارير على الهاتف المحمول
 */

const http = require('http');
const path = require('path');
const fs = require('fs');
const url = require('url');

class MobileServer {
    constructor(port = 3000) {
        this.port = port;
        this.server = null;
        this.isRunning = false;
        this.staticPath = path.join(__dirname);
    }

    /**
     * بدء تشغيل الخادم
     */
    start() {
        return new Promise((resolve, reject) => {
            try {
                this.server = http.createServer((req, res) => {
                    this.handleRequest(req, res);
                });

                this.server.listen(this.port, () => {
                    this.isRunning = true;
                    console.log(`🌐 [MOBILE-SERVER] الخادم يعمل على http://localhost:${this.port}`);
                    resolve(this.port);
                });

                this.server.on('error', (error) => {
                    console.error('❌ [MOBILE-SERVER] خطأ في الخادم:', error);
                    reject(error);
                });

            } catch (error) {
                console.error('❌ [MOBILE-SERVER] خطأ في بدء الخادم:', error);
                reject(error);
            }
        });
    }

    /**
     * إيقاف الخادم
     */
    stop() {
        return new Promise((resolve) => {
            if (this.server && this.isRunning) {
                this.server.close(() => {
                    this.isRunning = false;
                    console.log('🛑 [MOBILE-SERVER] تم إيقاف الخادم');
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }

    /**
     * معالجة طلبات HTTP
     */
    handleRequest(req, res) {
        try {
            const parsedUrl = url.parse(req.url, true);
            const pathname = parsedUrl.pathname;

            console.log(`📱 [MOBILE-SERVER] طلب: ${req.method} ${pathname}`);

            // تعيين headers للـ CORS
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

            if (req.method === 'OPTIONS') {
                res.writeHead(200);
                res.end();
                return;
            }

            // توجيه الطلبات
            if (pathname === '/' || pathname === '/view') {
                this.serveMobileViewer(req, res);
            } else if (pathname === '/mobile-viewer.html') {
                this.serveMobileViewer(req, res);
            } else if (pathname === '/mobile-viewer.js') {
                this.serveFile(res, 'mobile-viewer.js', 'application/javascript');
            } else if (pathname === '/qr-data-manager-client.js') {
                this.serveFile(res, 'qr-data-manager-client.js', 'application/javascript');
            } else if (pathname === '/api/decode') {
                this.handleDecodeAPI(req, res);
            } else if (pathname === '/health') {
                this.handleHealthCheck(req, res);
            } else {
                this.serve404(res);
            }

        } catch (error) {
            console.error('❌ [MOBILE-SERVER] خطأ في معالجة الطلب:', error);
            this.serve500(res, error.message);
        }
    }

    /**
     * عرض صفحة العارض المتجاوب
     */
    serveMobileViewer(req, res) {
        try {
            const filePath = path.join(this.staticPath, 'mobile-viewer.html');
            
            if (fs.existsSync(filePath)) {
                const content = fs.readFileSync(filePath, 'utf8');
                
                res.writeHead(200, {
                    'Content-Type': 'text/html; charset=utf-8',
                    'Cache-Control': 'no-cache'
                });
                res.end(content);
            } else {
                this.serve404(res);
            }

        } catch (error) {
            console.error('❌ [MOBILE-SERVER] خطأ في عرض الصفحة:', error);
            this.serve500(res, error.message);
        }
    }

    /**
     * عرض ملف
     */
    serveFile(res, filename, contentType) {
        try {
            const filePath = path.join(this.staticPath, filename);
            
            if (fs.existsSync(filePath)) {
                const content = fs.readFileSync(filePath, 'utf8');
                
                res.writeHead(200, {
                    'Content-Type': contentType + '; charset=utf-8',
                    'Cache-Control': 'no-cache'
                });
                res.end(content);
            } else {
                this.serve404(res);
            }

        } catch (error) {
            console.error(`❌ [MOBILE-SERVER] خطأ في عرض الملف ${filename}:`, error);
            this.serve500(res, error.message);
        }
    }

    /**
     * API لفك تشفير البيانات
     */
    handleDecodeAPI(req, res) {
        if (req.method !== 'POST') {
            res.writeHead(405, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Method not allowed' }));
            return;
        }

        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });

        req.on('end', () => {
            try {
                const data = JSON.parse(body);
                
                // هنا يمكن إضافة منطق فك التشفير
                // للبساطة، سنعيد البيانات كما هي
                
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: true,
                    data: data
                }));

            } catch (error) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    success: false,
                    error: 'Invalid JSON data'
                }));
            }
        });
    }

    /**
     * فحص صحة الخادم
     */
    handleHealthCheck(req, res) {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            status: 'ok',
            server: 'Tasfiya Pro Mobile Server',
            version: '1.0.0',
            timestamp: new Date().toISOString()
        }));
    }

    /**
     * صفحة 404
     */
    serve404(res) {
        const html = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>الصفحة غير موجودة - تصفية برو</title>
            <style>
                body { font-family: 'Cairo', Arial, sans-serif; text-align: center; padding: 50px; background: #f8f9fa; }
                .error-container { max-width: 500px; margin: 0 auto; }
                h1 { color: #dc3545; font-size: 3rem; margin-bottom: 20px; }
                p { color: #6c757d; font-size: 1.2rem; margin-bottom: 30px; }
                .btn { background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="error-container">
                <h1>404</h1>
                <p>الصفحة المطلوبة غير موجودة</p>
                <a href="/" class="btn">العودة للصفحة الرئيسية</a>
            </div>
        </body>
        </html>
        `;

        res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(html);
    }

    /**
     * صفحة خطأ 500
     */
    serve500(res, errorMessage) {
        const html = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>خطأ في الخادم - تصفية برو</title>
            <style>
                body { font-family: 'Cairo', Arial, sans-serif; text-align: center; padding: 50px; background: #f8f9fa; }
                .error-container { max-width: 500px; margin: 0 auto; }
                h1 { color: #dc3545; font-size: 3rem; margin-bottom: 20px; }
                p { color: #6c757d; font-size: 1.2rem; margin-bottom: 30px; }
                .error-details { background: #fff; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: left; }
            </style>
        </head>
        <body>
            <div class="error-container">
                <h1>خطأ في الخادم</h1>
                <p>حدث خطأ أثناء معالجة طلبك</p>
                <div class="error-details">
                    <strong>تفاصيل الخطأ:</strong><br>
                    ${errorMessage}
                </div>
            </div>
        </body>
        </html>
        `;

        res.writeHead(500, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(html);
    }

    /**
     * الحصول على حالة الخادم
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            port: this.port,
            url: this.isRunning ? `http://localhost:${this.port}` : null
        };
    }

    /**
     * تغيير المنفذ
     */
    setPort(newPort) {
        if (!this.isRunning) {
            this.port = newPort;
            return true;
        }
        return false;
    }
}

module.exports = MobileServer;
